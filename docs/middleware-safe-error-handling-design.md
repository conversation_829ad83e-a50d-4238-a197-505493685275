# Middleware-Safe Error Handling Design

## Ultimate Electrical Designer - <PERSON><PERSON><PERSON> Redesign

**Document Version:** 1.0  
**Date:** August 7, 2025  
**Task:** 1.1.2 - Design Middleware-Safe Error Handling Pattern  

---

## Problem Statement

The current unified error handler raises `HTTPException` directly within async middleware contexts, causing ExceptionGroup errors in <PERSON><PERSON>'s anyio TaskGroup. This breaks the middleware stack and prevents proper error propagation.

## Root Cause Analysis

### Current Problematic Flow
```
Service Exception → <PERSON>rror Handler → HTTPException (line 910) → anyio TaskGroup → ExceptionGroup
```

### Middleware Stack Context
```
Starlette BaseMiddleware (anyio.create_task_group)
├── SecurityMiddleware (@handle_security_errors)
├── ContextMiddleware  
├── LoggingMiddleware
└── API Routes
```

## Solution Design: Exception Boundary Pattern

### Core Principle
**Never raise HTTPException within anyio TaskGroup context**. Instead, use a two-phase error handling approach:

1. **Phase 1**: Catch and convert exceptions to application-specific exceptions
2. **Phase 2**: Convert application exceptions to HTTPException at the FastAPI level

### New Error Handling Pattern

#### 1. Middleware-Safe Exception Types
Create middleware-safe exception types that can be safely raised within async contexts:

```python
class MiddlewareSafeException(BaseApplicationException):
    """Base exception that can be safely raised in middleware context."""
    def __init__(self, message: str, status_code: int = 500, detail: str = None):
        super().__init__(message)
        self.status_code = status_code
        self.detail = detail or message

class SecurityMiddlewareException(MiddlewareSafeException):
    """Security-related middleware exception."""
    pass

class DatabaseMiddlewareException(MiddlewareSafeException):
    """Database-related middleware exception."""
    pass
```

#### 2. Middleware-Safe Error Handler Decorators
Redesign decorators to raise middleware-safe exceptions instead of HTTPException:

```python
def handle_security_errors_safe(security_operation: str = "security_check"):
    """Middleware-safe version of handle_security_errors."""
    
    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(func)
        async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                result = func(*args, **kwargs)
                if inspect.iscoroutine(result):
                    return await result
                return result
            except Exception as e:
                # Pass through existing middleware-safe exceptions
                if isinstance(e, MiddlewareSafeException):
                    raise
                
                # Pass through certain exceptions
                if isinstance(e, (DataValidationError, NotFoundError)):
                    raise
                
                # Convert other exceptions to middleware-safe exceptions
                logger.exception(f"Security operation '{security_operation}' failed")
                
                result = unified_error_handler.handle_exception(
                    e, ErrorContext.SECURITY,
                    additional_context={
                        "security_operation": security_operation,
                        "function": func.__name__,
                    }
                )
                
                # Raise middleware-safe exception instead of HTTPException
                raise SecurityMiddlewareException(
                    message=f"Security operation '{security_operation}' failed: {str(e)}",
                    status_code=result.http_status_code,
                    detail=result.error_response.detail
                )
        
        return async_wrapper
    return decorator
```

#### 3. FastAPI Exception Handler
Add a global exception handler at the FastAPI level to convert middleware-safe exceptions to HTTPException:

```python
@app.exception_handler(MiddlewareSafeException)
async def middleware_safe_exception_handler(request: Request, exc: MiddlewareSafeException):
    """Convert middleware-safe exceptions to proper HTTP responses."""
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail}
    )
```

### Exception Boundary Definition

#### Safe Zone (No HTTPException)
- **Middleware Layer**: All middleware decorators and dispatch methods
- **Service Layer**: Business logic and service methods  
- **Repository Layer**: Database access methods
- **anyio TaskGroup Context**: Any code running within Starlette middleware

#### Conversion Zone (HTTPException Allowed)
- **FastAPI Exception Handlers**: Global exception handlers
- **API Route Handlers**: Direct endpoint implementations
- **FastAPI Dependency Injection**: Dependency providers

### Middleware Layer Interaction Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                    FastAPI Application                     │
├─────────────────────────────────────────────────────────────┤
│  Exception Handlers (HTTPException Conversion Zone)        │
│  ├── MiddlewareSafeException → HTTPException               │
│  └── Other Application Exceptions → HTTPException          │
└─────────────────────────────────────────────────────────────┘
                              │
                    MiddlewareSafeException
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Starlette Middleware Stack                 │
│                  (Safe Zone - No HTTPException)            │
├─────────────────────────────────────────────────────────────┤
│  anyio.create_task_group() Context                        │
│  ├── SecurityMiddleware (@handle_security_errors_safe)     │
│  ├── ContextMiddleware                                     │
│  ├── LoggingMiddleware                                     │
│  └── Other Middleware                                      │
└─────────────────────────────────────────────────────────────┘
                              │
                    Service/Repository Exceptions
                              │
┌─────────────────────────────────────────────────────────────┐
│                   Application Layers                       │
├─────────────────────────────────────────────────────────────┤
│  API Routes → Services → Repositories → Models             │
│  (Application Exceptions Only)                             │
└─────────────────────────────────────────────────────────────┘
```

## Implementation Strategy

### Phase 1: Create Middleware-Safe Exception Types
1. Define `MiddlewareSafeException` base class
2. Create specific exception types for each context
3. Ensure proper inheritance from `BaseApplicationException`

### Phase 2: Update Error Handler Decorators
1. Create `handle_security_errors_safe` decorator
2. Create `handle_database_errors_safe` decorator  
3. Create `handle_api_errors_safe` decorator
4. Maintain backward compatibility with existing decorators

### Phase 3: Add FastAPI Exception Handlers
1. Register global exception handler for `MiddlewareSafeException`
2. Ensure proper HTTP status code mapping
3. Maintain error response format consistency

### Phase 4: Update Middleware Usage
1. Replace `@handle_security_errors` with `@handle_security_errors_safe` in middleware
2. Update other middleware decorators as needed
3. Test middleware stack integration

## Success Criteria

### Functional Requirements
- ✅ No ExceptionGroup errors in middleware stack
- ✅ Proper HTTP status codes in API responses
- ✅ Consistent error response format
- ✅ Backward compatibility with existing error handling

### Technical Requirements
- ✅ Exception boundary clearly defined and enforced
- ✅ Middleware-safe exceptions never raise HTTPException in async context
- ✅ FastAPI exception handlers properly convert to HTTP responses
- ✅ Error logging and tracking maintained

### Quality Requirements
- ✅ Zero tolerance policy compliance
- ✅ 5-layer architecture integrity maintained
- ✅ Professional error handling standards
- ✅ Comprehensive test coverage

## Risk Mitigation

### High-Risk Areas
1. **Breaking Changes**: New exception types might break existing code
   - **Mitigation**: Maintain backward compatibility, gradual migration
2. **Performance Impact**: Additional exception handling layers
   - **Mitigation**: Minimal overhead, benchmark performance
3. **Error Message Consistency**: Different exception paths might produce inconsistent messages
   - **Mitigation**: Centralized error message formatting

### Testing Strategy
1. **Unit Tests**: Test each decorator in isolation
2. **Integration Tests**: Test full middleware stack with error scenarios
3. **Performance Tests**: Ensure no significant performance degradation
4. **Compatibility Tests**: Verify existing error handling still works

## Next Steps

This design provides the foundation for implementing middleware-safe error handling. The next task (1.1.3) will implement this design in the unified error handler code.

**Quality Gate**: ✅ Design approved for middleware compatibility
- Exception boundary clearly defined
- Middleware-safe pattern specified
- Implementation strategy outlined
- Risk mitigation planned
