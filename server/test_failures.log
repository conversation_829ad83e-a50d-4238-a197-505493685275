2025-08-07 16:36:45,087 - ERROR - Test Failed: tests/api/v1/test_project_routes.py::TestProjectMemberRoutes::test_add_project_member_success
Failure Details:
tests/api/v1/test_project_routes.py:18: in test_add_project_member_success
    assert response.status_code == 201
E   assert 404 == 201
E    +  where 404 = <Response [404 Not Found]>.status_code
2025-08-07 16:36:46,973 - CRITICAL - Error in setup: tests/api/v1/test_project_routes.py::TestProjectMemberRoutes::test_add_project_member_duplicate_entry
Error Details:
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:696: in pytest_fixture_setup
    hook_result = yield
                  ^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:321: in _async_fixture_wrapper
    result = runner.run(setup(), context=context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py:118: in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py:725: in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:317: in setup
    res = await fixture_function(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/conftest.py:857: in test_project_member
    member = await member_service.add_member_to_project(test_project.id, member_create)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/services/general/project_member_service.py:56: in add_member_to_project
    raise ProjectNotFoundError(project_id=str(project_id))
E   src.core.errors.exceptions.ProjectNotFoundError: Project with ID '961' not found.
2025-08-07 16:36:47,918 - CRITICAL - Error in setup: tests/api/v1/test_project_routes.py::TestProjectMemberRoutes::test_remove_project_member_success
Error Details:
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:696: in pytest_fixture_setup
    hook_result = yield
                  ^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:321: in _async_fixture_wrapper
    result = runner.run(setup(), context=context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py:118: in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py:725: in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:317: in setup
    res = await fixture_function(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/conftest.py:857: in test_project_member
    member = await member_service.add_member_to_project(test_project.id, member_create)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/services/general/project_member_service.py:56: in add_member_to_project
    raise ProjectNotFoundError(project_id=str(project_id))
E   src.core.errors.exceptions.ProjectNotFoundError: Project with ID '962' not found.
2025-08-07 16:36:49,181 - CRITICAL - Error in setup: tests/api/v1/test_project_routes.py::TestProjectMemberRoutes::test_update_project_member_success
Error Details:
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:696: in pytest_fixture_setup
    hook_result = yield
                  ^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:321: in _async_fixture_wrapper
    result = runner.run(setup(), context=context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py:118: in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py:725: in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:317: in setup
    res = await fixture_function(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/conftest.py:857: in test_project_member
    member = await member_service.add_member_to_project(test_project.id, member_create)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/services/general/project_member_service.py:56: in add_member_to_project
    raise ProjectNotFoundError(project_id=str(project_id))
E   src.core.errors.exceptions.ProjectNotFoundError: Project with ID '964' not found.
2025-08-07 16:36:50,430 - CRITICAL - Error in setup: tests/api/v1/test_project_routes.py::TestProjectMemberRoutes::test_list_project_members_success
Error Details:
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:696: in pytest_fixture_setup
    hook_result = yield
                  ^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:321: in _async_fixture_wrapper
    result = runner.run(setup(), context=context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py:118: in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py:725: in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:317: in setup
    res = await fixture_function(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/conftest.py:857: in test_project_member
    member = await member_service.add_member_to_project(test_project.id, member_create)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/services/general/project_member_service.py:56: in add_member_to_project
    raise ProjectNotFoundError(project_id=str(project_id))
E   src.core.errors.exceptions.ProjectNotFoundError: Project with ID '965' not found.
